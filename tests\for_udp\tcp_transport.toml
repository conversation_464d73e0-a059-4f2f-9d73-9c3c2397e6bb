[client]
remote_addr = "127.0.0.1:2332" 
default_token = "default_token_if_not_specify" 

[client.transport]
type = "tcp" 

[client.services.echo] 
type = "udp"
local_addr = "127.0.0.1:8080" 
[client.services.pingpong] 
type = "udp"
local_addr = "127.0.0.1:8081" 

[server]
bind_addr = "0.0.0.0:2332" 
default_token = "default_token_if_not_specify" 

[server.transport]
type = "tcp" 

[server.services.echo] 
type = "udp"
bind_addr = "0.0.0.0:2334" 
[server.services.pingpong] 
type = "udp"
bind_addr = "0.0.0.0:2335" 
