name: Rust

on:
  pull_request:
    branches: ["*"]
  push:
    branches: ["main", "dev"]

concurrency:
  # Documentation suggests ${{ github.head_ref }}, but that's only available on pull_request/pull_request_target triggers, so using ${{ github.ref }}.
  # On main, we want all builds to complete even if merging happens faster to make it easier to discover at which point something broke.
  group: ${{ github.ref == 'refs/heads/main' && format('ci-main-{0}', github.sha) || format('ci-{0}', github.ref) }}
  cancel-in-progress: true

env:
  CARGO_TERM_COLOR: always

jobs:
  lints:
    name: Lints
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          components: clippy
      - uses: Swatinem/rust-cache@v1
      - name: Clippy
        run: cargo clippy -- -D warnings
      - name: Setup cargo-hack
        run: cargo install cargo-hack
      - name: Check all features
        run: >
          cargo hack check --feature-powerset --no-dev-deps
          --mutually-exclusive-features default,native-tls,websocket-native-tls,rustls,websocket-rustls

  build:
    name: Build for ${{ matrix.target }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            exe: rathole
            target: x86_64-unknown-linux-gnu
          - os: windows-latest
            exe: rathole.exe
            target: x86_64-pc-windows-msvc
          - os: macos-latest
            exe: rathole
            target: x86_64-apple-darwin
          - os: macos-latest
            exe: rathole
            target: aarch64-apple-darwin

    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
      - uses: Swatinem/rust-cache@v1
      - name: Build
        run: cargo build
      - name: Run tests with native-tls
        run: cargo test --verbose
      - name: Run tests with rustls
        run: cargo test --verbose --no-default-features --features server,client,rustls,noise,websocket-rustls,hot-reload
      - uses: actions/upload-artifact@v4
        with:
          name: rathole-${{ matrix.target }}
          path: target/debug/${{ matrix.exe }}
