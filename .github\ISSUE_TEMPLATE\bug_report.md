---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug
assignees: ''

---

<!-- Please try the latest release before filing a bug report -->

**Describe the bug**
<!-- A clear and concise description of what the bug is. -->

**To Reproduce**
Steps to reproduce the behavior:
1. 
2. 

**Configuration**
Configuration used to reproduce the behavior:

**Logs**
<!-- Please upload full client and server logs if possible, with sensitive information masked.
If you encountered a panic, please re-run with `RUST_BACKTRACE=1` to provide the backtrace. -->

**Environment:**
 - OS: <!-- Please fill in distribution if you're using linux-->
- `rathole --version` output: 
- CPU architecture:
- rustc version:
