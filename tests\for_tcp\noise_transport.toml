[client]
remote_addr = "127.0.0.1:2333" 
default_token = "default_token_if_not_specify" 

[client.transport]
type = "noise" 
[client.transport.noise]
remote_public_key = "mEnUEACy9UrTBmwoCJb6fcKWBRdvfD9XzuBVsroOLFg="

[client.services.echo] 
local_addr = "127.0.0.1:8080" 
[client.services.pingpong] 
local_addr = "127.0.0.1:8081" 

[server]
bind_addr = "0.0.0.0:2333" 
default_token = "default_token_if_not_specify" 

[server.transport]
type = "noise" 
[server.transport.noise]
local_private_key = "kQiSRtS3bs8BoGCJYgFnl1FLrTG1lV53Dj8jSjmg8tE="

[server.services.echo] 
bind_addr = "0.0.0.0:2334" 
[server.services.pingpong] 
bind_addr = "0.0.0.0:2335" 
