[client]
remote_addr = "127.0.0.1:2332" 
default_token = "default_token_if_not_specify" 

[client.transport]
type = "websocket"
[client.transport.tls]
trusted_root = "examples/tls/rootCA.crt"
hostname = "localhost"
[client.transport.websocket] 
tls = true

[client.services.echo] 
type = "udp"
local_addr = "127.0.0.1:8080" 
[client.services.pingpong] 
type = "udp"
local_addr = "127.0.0.1:8081" 

[server]
bind_addr = "0.0.0.0:2332" 
default_token = "default_token_if_not_specify" 

[server.transport]
type = "websocket" 
[server.transport.tls]
pkcs12 = "examples/tls/identity.pfx"
pkcs12_password = "1234"
[server.transport.websocket] 
tls = true

[server.services.echo] 
type = "udp"
bind_addr = "0.0.0.0:2334" 
[server.services.pingpong] 
type = "udp"
bind_addr = "0.0.0.0:2335" 
