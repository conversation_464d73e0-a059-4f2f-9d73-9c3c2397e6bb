name: Release

on:
  push:
    tags:
      - "*"

  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always

jobs:
  release:
    name: Cross build for ${{ matrix.target }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            exe: rathole
            cross: false
          - os: ubuntu-latest
            target: x86_64-unknown-linux-musl
            exe: rathole
            cross: false
          - os: ubuntu-latest
            target: aarch64-unknown-linux-musl
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: arm-unknown-linux-musleabi
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: arm-unknown-linux-musleabihf
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: armv7-unknown-linux-musleabihf
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: mips-unknown-linux-gnu
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: mips-unknown-linux-musl
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: mipsel-unknown-linux-gnu
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: mipsel-unknown-linux-musl
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: mips64-unknown-linux-gnuabi64
            exe: rathole
            cross: true
          - os: ubuntu-latest
            target: mips64el-unknown-linux-gnuabi64
            exe: rathole
            cross: true

          - os: macos-latest
            target: x86_64-apple-darwin
            exe: rathole
            cross: false
          
          - os: macos-latest
            target: aarch64-apple-darwin
            exe: rathole
            cross: false

          - os: windows-latest
            target: x86_64-pc-windows-msvc
            exe: rathole.exe
            cross: false
    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          default: true
      
      - name: Install OpenSSL
        if: matrix.os == 'ubuntu-latest'
        run: sudo apt-get install pkg-config libssl-dev
      - name: Install OpenSSL
        if: matrix.os == 'macos-latest'
        run: brew install openssl@3
      
      # Native build
      - name: Install target
        if: matrix.cross == false
        run: rustup target add ${{ matrix.target }}
      - name: Run tests
        if: matrix.cross == false && matrix.target != 'aarch64-apple-darwin'
        run: cargo test --release --target ${{ matrix.target }} --verbose 
      - name: Build release
        if: matrix.cross == false 
        run: cargo build --release --target ${{ matrix.target }}

      # Cross build
      - name: Install cross
        if: matrix.cross
        run: cargo install --version 0.2.5 cross
      - name: Run tests
        if: matrix.cross
        run: cross test --release --target ${{ matrix.target }} --verbose --features embedded --no-default-features
      - name: Build release
        if: matrix.cross
        run: cross build --release --target ${{ matrix.target }} --features embedded --no-default-features

      - name: Run UPX
        # Upx may not support some platforms. Ignore the errors
        continue-on-error: true
        # Disable upx for mips. See https://github.com/upx/upx/issues/387
        if: matrix.os == 'ubuntu-latest' && !contains(matrix.target, 'mips')
        uses: crazy-max/ghaction-upx@v1
        with:
          version: v4.0.2
          files: target/${{ matrix.target }}/release/${{ matrix.exe }}
          args: -q --best --lzma
      - uses: actions/upload-artifact@v4
        with:
          name: rathole-${{ matrix.target }}
          path: target/${{ matrix.target }}/release/${{ matrix.exe }}
      - name: Zip Release
        uses: TheDoctor0/zip-release@0.6.1
        with:
          type: zip
          filename: rathole-${{ matrix.target }}.zip
          directory: target/${{ matrix.target }}/release/
          path: ${{ matrix.exe }}
      - name: Publish
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: target/${{ matrix.target }}/release/rathole-${{ matrix.target }}.zip
          generate_release_notes: true
          draft: true
  docker:
    name: Publish to Docker Hub
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    needs: release
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          push: true
          platforms: linux/amd64, linux/arm64, linux/armhf, linux/armv7
          tags: rapiz1/rathole:latest, rapiz1/rathole:${{ github.ref_name }}
  publish-crate:
    name: Publish to crates.io
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    needs: release
    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
      - name: Publish
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CRATES_IO_API_TOKEN }}
        run: cargo publish
